# Files in the config/locales directory are used for internationalization and
# are automatically loaded by Rails. If you want to use locales other than
# English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more about the API, please read the Rails Internationalization guide
# at https://guides.rubyonrails.org/i18n.html.
#
# Be aware that YAML interprets the following case-insensitive strings as
# booleans: `true`, `false`, `on`, `off`, `yes`, `no`. Therefore, these strings
# must be quoted to be interpreted as strings. For example:
#
#     en:
#       "yes": yup
#       enabled: "ON"

en:  
  home_page:
      index:
        heading: "Schools Site"
  error:
    oops: "Oops, something went wrong."
    max_desc: "Max 160 characters"
  access_denied:    
    message: "You are not authorized to access this page."
  sidebar:
    view_site: "View site"    
  settings: 
    title: "Settings"
    site_name: "Site Name"
    site_description: "Site Description"      
    site_light_theme: "Site Light Theme"
    site_dark_theme: "Site Dark Theme"
    light_theme: "Light Theme"
    dark_theme: "Dark Theme"   
    user: "User Settings"
  posts:
    index:
      heading: "Posts"
    list: "List Posts"
    new:
      heading: "New Post"
    edit:
      heading: "Edit Post"