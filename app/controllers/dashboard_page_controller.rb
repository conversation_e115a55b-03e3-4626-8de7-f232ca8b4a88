class DashboardPageController < ApplicationController
  before_action :authorize_dashboard, only: [ :index ]
  before_action :authenticate_user!
  before_action :set_default_breadcrumb
  layout "dashboard"

  def index
    set_meta_tags title: "Dashboard"
  end

  def authorize_dashboard
    authorize! :read, :dashboard
  end

  private

  def set_default_breadcrumb
    helpers.add_breadcrumb "Dashboard", dashboard_root_path
  end
end
