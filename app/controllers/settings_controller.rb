class SettingsController < ApplicationController
  layout "dashboard"
  before_action :authorize_settings
  before_action :set_settings_breadcrumbs
  include SettingsHelper

  def index
    set_meta_tags title: t("settings.title")

    # Batch load all site settings
    site_settings = get_options({
      "site_name" => "Rorschools",
      "site_description" => "A school website",
      "site_light_theme" => "winter",
      "site_dark_theme" => "black"
    })

    @site_name = site_settings["site_name"]
    @site_description = site_settings["site_description"]
    @site_light_theme = site_settings["site_light_theme"]
    @site_dark_theme = site_settings["site_dark_theme"]

    # Batch load user settings
    user_settings = get_user_options(current_user, {
      "light_theme" => "winter",
      "dark_theme" => "black"
    })

    @user_light_theme = user_settings["light_theme"]
    @user_dark_theme = user_settings["dark_theme"]

    @light_theme_list = [
      "light", "cupcake", "bumblebee", "emerald", "corporate", "retro", "cyberpunk", "valentine", "garden", "aqua", "lofi", "pastel", "fantasy", "wireframe", "cmyk", "autumn", "acid", "lemonade", "winter", "nord", "caramellatte", "silk"
    ]
    @dark_theme_list = [
      "dark", "synthwave", "halloween", "forest", "black", "luxury", "dracula", "business", "night", "coffee", "dim", "sunset", "abyss"
    ]
  end

  def update
    setting_params.each do |key, value|
      if key.starts_with?("site_")
        update_option(key, value)
      else
        update_user_option(current_user, key, value)
      end
    end

    set_themes

    redirect_to dashboard_settings_path(t: Time.current.to_i), notice: "Settings updated successfully!"
  end

  private

  def authorize_settings
    authorize! :update, :settings
  end

  def setting_params
    params.require(:setting).permit(:site_name, :site_description, :site_light_theme, :site_dark_theme, :light_theme, :dark_theme)
  end

  def set_settings_breadcrumbs
    helpers.add_breadcrumb "Dashboard", dashboard_root_path
    helpers.add_breadcrumb "Settings", dashboard_settings_path
  end
end
