class ApplicationController < ActionController::Base
  include SettingsHelper
  before_action :configure_permitted_parameters, if: :devise_controller?
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  before_action :set_themes
  allow_browser versions: :modern

  # Switch locale based on params[:locale] https://guides.rubyonrails.org/i18n.html#managing-the-locale-across-requests
  around_action :switch_locale

  # Use different layout for devise controllers
  layout :layout_by_resource

  def switch_locale(&action)
    locale = params[:locale] || detect_locale || I18n.default_locale
    I18n.with_locale(locale, &action)
  end

  # Setting the Locale from URL Params
  def default_url_options
    { locale: I18n.locale }
  end

  # Detect locale from browser
  def detect_locale
     preferred_languages = request.env["HTTP_ACCEPT_LANGUAGE"]&.scan(/\w{2}/)&.map(&:downcase)
     available = I18n.available_locales.map(&:to_s)
     detected_locale = preferred_languages&.find { |lang| available.include?(lang) }
     detected_locale&.to_sym
  end

  def layout_by_resource
    if devise_controller? && (action_name == "new" || action_name == "create")
      "auth"
    elsif devise_controller? && (action_name == "edit" || action_name == "update")
      "dashboard"
    else
      "application"
    end
  end

  def after_sign_in_path_for(resource)
    if resource.viewer?
      root_path(locale: detect_locale)
    else
      dashboard_root_path(locale: detect_locale)
    end
  end

  def after_sign_up_path_for(resource)
    after_sign_in_path_for(resource)
  end

  rescue_from CanCan::AccessDenied do |exception|
    respond_to do |format|
      format.json { render nothing: true, status: :forbidden }
      format.html { redirect_to access_denied_path(locale: detect_locale, message: exception.message) }
      format.js   { render nothing: true, status: :forbidden }
    end
  end

  protected

  def set_themes
    # Batch load all required settings to minimize database queries
    settings = Setting.get_multiple({
      "site_light_theme" => "silk",
      "site_dark_theme" => "black",
      "site_name" => "Rorschools"
    })

    # Themes for the public-facing application (front)
    @application_light_theme = settings["site_light_theme"]
    @application_dark_theme = settings["site_dark_theme"]
    @site_name = settings["site_name"]

    # Batch load user settings for themes
    if current_user
      user_settings = UserSetting.get_multiple(current_user, {
        "light_theme" => "winter",
        "dark_theme" => "dark"
      })
      @user_light_theme = user_settings["light_theme"]
      @user_dark_theme = user_settings["dark_theme"]
    else
      @user_light_theme = "winter"
      @user_dark_theme = "dark"
    end
  end

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :avatar ])
    devise_parameter_sanitizer.permit(:account_update, keys: [ :avatar ])
  end
end
